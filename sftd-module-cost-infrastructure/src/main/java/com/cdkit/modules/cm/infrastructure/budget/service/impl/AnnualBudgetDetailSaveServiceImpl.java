package com.cdkit.modules.cm.infrastructure.budget.service.impl;

import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetailDirectCost;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 年度预算明细保存服务实现
 * 在基础设施层提供实际的数据库保存功能
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnualBudgetDetailSaveServiceImpl {

    private final ICostAnnualBudgetDetailService costAnnualBudgetDetailService;

    /**
     * 保存年度预算明细数据到数据库
     * 
     * @param budgetId 年度预算主表ID
     * @param detailDataList 明细数据列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDetailsToDatabase(String budgetId, List<BudgetDetailData> detailDataList) {
        if (detailDataList == null || detailDataList.isEmpty()) {
            log.info("没有明细数据需要保存到数据库，budgetId: {}", budgetId);
            return;
        }

        log.info("开始保存年度预算明细数据到数据库，budgetId: {}, 明细数量: {}", budgetId, detailDataList.size());

        try {
            for (BudgetDetailData detailData : detailDataList) {
                // 转换为基础设施层对象
                CostAnnualBudgetDetail detailPO = convertToDetailPO(detailData, budgetId);
                
                // 转换直接成本明细列表
                List<CostAnnualBudgetDetailDirectCost> directCostPOList = new ArrayList<>();
                if (detailData.getDirectCostList() != null && !detailData.getDirectCostList().isEmpty()) {
                    for (DirectCostData directCostData : detailData.getDirectCostList()) {
                        CostAnnualBudgetDetailDirectCost directCostPO = convertToDirectCostPO(directCostData, detailData.getId());
                        directCostPOList.add(directCostPO);
                    }
                }
                
                // 调用基础设施层保存方法（会自动处理关联关系）
                costAnnualBudgetDetailService.saveMain(detailPO, directCostPOList);
                
                log.info("成功保存明细记录到数据库：项目编号={}, 直接成本明细数量={}", 
                        detailData.getProjectCode(), directCostPOList.size());
            }
            
        } catch (Exception e) {
            log.error("保存年度预算明细数据到数据库失败，budgetId: {}", budgetId, e);
            throw new RuntimeException("保存明细数据失败：" + e.getMessage(), e);
        }

        log.info("年度预算明细数据保存到数据库完成，budgetId: {}", budgetId);
    }

    /**
     * 转换为基础设施层明细对象
     */
    private CostAnnualBudgetDetail convertToDetailPO(BudgetDetailData detailData, String budgetId) {
        CostAnnualBudgetDetail po = new CostAnnualBudgetDetail();
        
        po.setId(detailData.getId());
        po.setBudgetId(budgetId);
        po.setPlanId(detailData.getPlanId());
        po.setProjectCode(detailData.getProjectCode());
        po.setProjectName(detailData.getProjectName());
        po.setProfessionalCompany(detailData.getProfessionalCompany());
        po.setCenter(detailData.getCenter());
        po.setBudgetType(detailData.getBudgetType());
        po.setWbsCode(detailData.getWbsCode());
        po.setProjectType(detailData.getProjectType());
        po.setFourthLevelBusiness(detailData.getFourthLevelBusiness());
        // 注意：数据库字段是businessArea，但请求对象是businessSubcategory
        po.setBusinessArea(detailData.getBusinessSubcategory());
        po.setRevenueBudget(detailData.getRevenueBudget());
        po.setDirectCostBudget(detailData.getDirectCostBudget());
        po.setOtherCostBudget(detailData.getOtherCostBudget());
        po.setProfitBudget(detailData.getProfitBudget());
        
        return po;
    }

    /**
     * 转换为基础设施层直接成本对象
     */
    private CostAnnualBudgetDetailDirectCost convertToDirectCostPO(DirectCostData directCostData, String budgetDetailId) {
        CostAnnualBudgetDetailDirectCost po = new CostAnnualBudgetDetailDirectCost();
        
        po.setId(directCostData.getId());
        po.setBudgetDetailId(budgetDetailId);
        po.setCostItem(directCostData.getSubjectName());
        po.setCostDescription(directCostData.getSubjectDescription());
        po.setCostAmount(directCostData.getCostAmount());
        
        return po;
    }

    // 简单的数据传输对象，避免循环依赖
    public static class BudgetDetailData {
        private String id;
        private String planId;
        private String projectCode;
        private String projectName;
        private String professionalCompany;
        private String center;
        private String budgetType;
        private String wbsCode;
        private String projectType;
        private String fourthLevelBusiness;
        private String businessSubcategory;
        private BigDecimal revenueBudget;
        private BigDecimal directCostBudget;
        private BigDecimal otherCostBudget;
        private BigDecimal profitBudget;
        private List<DirectCostData> directCostList;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getPlanId() { return planId; }
        public void setPlanId(String planId) { this.planId = planId; }
        public String getProjectCode() { return projectCode; }
        public void setProjectCode(String projectCode) { this.projectCode = projectCode; }
        public String getProjectName() { return projectName; }
        public void setProjectName(String projectName) { this.projectName = projectName; }
        public String getProfessionalCompany() { return professionalCompany; }
        public void setProfessionalCompany(String professionalCompany) { this.professionalCompany = professionalCompany; }
        public String getCenter() { return center; }
        public void setCenter(String center) { this.center = center; }
        public String getBudgetType() { return budgetType; }
        public void setBudgetType(String budgetType) { this.budgetType = budgetType; }
        public String getWbsCode() { return wbsCode; }
        public void setWbsCode(String wbsCode) { this.wbsCode = wbsCode; }
        public String getProjectType() { return projectType; }
        public void setProjectType(String projectType) { this.projectType = projectType; }
        public String getFourthLevelBusiness() { return fourthLevelBusiness; }
        public void setFourthLevelBusiness(String fourthLevelBusiness) { this.fourthLevelBusiness = fourthLevelBusiness; }
        public String getBusinessSubcategory() { return businessSubcategory; }
        public void setBusinessSubcategory(String businessSubcategory) { this.businessSubcategory = businessSubcategory; }
        public BigDecimal getRevenueBudget() { return revenueBudget; }
        public void setRevenueBudget(BigDecimal revenueBudget) { this.revenueBudget = revenueBudget; }
        public BigDecimal getDirectCostBudget() { return directCostBudget; }
        public void setDirectCostBudget(BigDecimal directCostBudget) { this.directCostBudget = directCostBudget; }
        public BigDecimal getOtherCostBudget() { return otherCostBudget; }
        public void setOtherCostBudget(BigDecimal otherCostBudget) { this.otherCostBudget = otherCostBudget; }
        public BigDecimal getProfitBudget() { return profitBudget; }
        public void setProfitBudget(BigDecimal profitBudget) { this.profitBudget = profitBudget; }
        public List<DirectCostData> getDirectCostList() { return directCostList; }
        public void setDirectCostList(List<DirectCostData> directCostList) { this.directCostList = directCostList; }
    }

    public static class DirectCostData {
        private String id;
        private String subjectCode;
        private String subjectName;
        private String subjectDescription;
        private BigDecimal costAmount;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getSubjectCode() { return subjectCode; }
        public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }
        public String getSubjectName() { return subjectName; }
        public void setSubjectName(String subjectName) { this.subjectName = subjectName; }
        public String getSubjectDescription() { return subjectDescription; }
        public void setSubjectDescription(String subjectDescription) { this.subjectDescription = subjectDescription; }
        public BigDecimal getCostAmount() { return costAmount; }
        public void setCostAmount(BigDecimal costAmount) { this.costAmount = costAmount; }
    }
}
