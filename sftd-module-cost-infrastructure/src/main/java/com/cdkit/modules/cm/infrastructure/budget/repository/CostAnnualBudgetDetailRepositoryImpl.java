package com.cdkit.modules.cm.infrastructure.budget.repository;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailRepository;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetailDirectCost;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetDetailService;
import com.cdkit.modules.cm.infrastructure.budget.service.ICostAnnualBudgetDetailDirectCostService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 年度预算明细仓储实现
 * <AUTHOR>
 * @date 2025-08-04
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CostAnnualBudgetDetailRepositoryImpl implements CostAnnualBudgetDetailRepository {

    private final ICostAnnualBudgetDetailService costAnnualBudgetDetailService;
    private final ICostAnnualBudgetDetailDirectCostService costAnnualBudgetDetailDirectCostService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBudgetDetails(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            log.info("没有明细数据需要保存到数据库，budgetId: {}", budgetId);
            return;
        }

        log.info("开始保存年度预算明细数据到数据库，budgetId: {}, 明细数量: {}", budgetId, budgetDetailList.size());

        try {
            for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
                // 转换为基础设施层对象
                CostAnnualBudgetDetail detailPO = convertToDetailPO(detailInfo, budgetId);
                
                // 转换直接成本明细列表
                List<CostAnnualBudgetDetailDirectCost> directCostPOList = new ArrayList<>();
                if (detailInfo.getDirectCostList() != null && !detailInfo.getDirectCostList().isEmpty()) {
                    for (CostAnnualBudgetEntity.DirectCostInfo directCostInfo : detailInfo.getDirectCostList()) {
                        CostAnnualBudgetDetailDirectCost directCostPO = convertToDirectCostPO(directCostInfo, detailInfo.getId());
                        directCostPOList.add(directCostPO);
                    }
                }
                
                // 调用基础设施层保存方法（会自动处理关联关系）
                costAnnualBudgetDetailService.saveMain(detailPO, directCostPOList);
                
                log.info("成功保存明细记录到数据库：项目编号={}, 直接成本明细数量={}", 
                        detailInfo.getProjectCode(), directCostPOList.size());
            }
            
        } catch (Exception e) {
            log.error("保存年度预算明细数据到数据库失败，budgetId: {}", budgetId, e);
            throw new RuntimeException("保存明细数据失败：" + e.getMessage(), e);
        }

        log.info("年度预算明细数据保存到数据库完成，budgetId: {}", budgetId);
    }

    @Override
    public List<CostAnnualBudgetEntity.BudgetDetailInfo> findByBudgetId(String budgetId) {
        log.info("查询年度预算明细数据，budgetId: {}", budgetId);

        if (budgetId == null || budgetId.trim().isEmpty()) {
            log.warn("预算ID为空，返回空列表");
            return new ArrayList<>();
        }

        try {
            // 查询明细数据
            List<CostAnnualBudgetDetail> detailPOList = costAnnualBudgetDetailService.selectByMainId(budgetId);
            if (detailPOList == null || detailPOList.isEmpty()) {
                log.info("未找到明细数据，budgetId: {}", budgetId);
                return new ArrayList<>();
            }

            List<CostAnnualBudgetEntity.BudgetDetailInfo> resultList = new ArrayList<>();

            for (CostAnnualBudgetDetail detailPO : detailPOList) {
                // 转换明细数据
                CostAnnualBudgetEntity.BudgetDetailInfo detailInfo = convertToDetailInfo(detailPO);

                // 查询并转换直接成本明细
                List<CostAnnualBudgetDetailDirectCost> directCostPOList =
                    costAnnualBudgetDetailDirectCostService.selectByMainId(detailPO.getId());

                if (directCostPOList != null && !directCostPOList.isEmpty()) {
                    List<CostAnnualBudgetEntity.DirectCostInfo> directCostInfoList = new ArrayList<>();
                    for (CostAnnualBudgetDetailDirectCost directCostPO : directCostPOList) {
                        CostAnnualBudgetEntity.DirectCostInfo directCostInfo = convertToDirectCostInfo(directCostPO);
                        directCostInfoList.add(directCostInfo);
                    }
                    detailInfo.setDirectCostList(directCostInfoList);
                }

                resultList.add(detailInfo);
            }

            log.info("查询年度预算明细数据成功，budgetId: {}, 明细数量: {}", budgetId, resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("查询年度预算明细数据失败，budgetId: {}", budgetId, e);
            throw new RuntimeException("查询明细数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByBudgetId(String budgetId) {
        // TODO: 实现删除逻辑
        log.info("删除年度预算明细数据，budgetId: {}", budgetId);
    }

    /**
     * 转换为基础设施层明细对象
     */
    private CostAnnualBudgetDetail convertToDetailPO(CostAnnualBudgetEntity.BudgetDetailInfo detailInfo, String budgetId) {
        CostAnnualBudgetDetail po = new CostAnnualBudgetDetail();
        
        po.setId(detailInfo.getId());
        po.setBudgetId(budgetId);
        po.setPlanId(detailInfo.getPlanId());
        po.setProjectCode(detailInfo.getProjectCode());
        po.setProjectName(detailInfo.getProjectName());
        po.setProfessionalCompany(detailInfo.getProfessionalCompany());
        po.setCenter(detailInfo.getCenter());
        po.setBudgetType(detailInfo.getBudgetType());
        po.setWbsCode(detailInfo.getWbsCode());
        po.setProjectType(detailInfo.getProjectType());
        po.setFourthLevelBusiness(detailInfo.getFourthLevelBusiness());
        // 注意：数据库字段是businessArea，但领域实体是businessSubcategory
        po.setBusinessArea(detailInfo.getBusinessSubcategory());
        po.setRevenueBudget(detailInfo.getRevenueBudget());
        po.setDirectCostBudget(detailInfo.getDirectCostBudget());
        po.setOtherCostBudget(detailInfo.getOtherCostBudget());
        po.setProfitBudget(detailInfo.getProfitBudget());
        
        return po;
    }

    /**
     * 转换为基础设施层直接成本对象
     */
    private CostAnnualBudgetDetailDirectCost convertToDirectCostPO(CostAnnualBudgetEntity.DirectCostInfo directCostInfo, String budgetDetailId) {
        CostAnnualBudgetDetailDirectCost po = new CostAnnualBudgetDetailDirectCost();
        
        po.setId(directCostInfo.getId());
        po.setBudgetDetailId(budgetDetailId);
        po.setCostItem(directCostInfo.getSubjectName());
        po.setCostDescription(directCostInfo.getSubjectDescription());
        po.setCostAmount(directCostInfo.getCostAmount());
        
        return po;
    }

    /**
     * 数据库对象转换为领域实体明细
     */
    private CostAnnualBudgetEntity.BudgetDetailInfo convertToDetailInfo(CostAnnualBudgetDetail detailPO) {
        CostAnnualBudgetEntity.BudgetDetailInfo detailInfo = new CostAnnualBudgetEntity.BudgetDetailInfo();

        detailInfo.setId(detailPO.getId());
        detailInfo.setBudgetId(detailPO.getBudgetId());
        detailInfo.setPlanId(detailPO.getPlanId());
        detailInfo.setProjectCode(detailPO.getProjectCode());
        detailInfo.setProjectName(detailPO.getProjectName());
        detailInfo.setProfessionalCompany(detailPO.getProfessionalCompany());
        detailInfo.setCenter(detailPO.getCenter());
        detailInfo.setBudgetType(detailPO.getBudgetType());
        detailInfo.setWbsCode(detailPO.getWbsCode());
        detailInfo.setProjectType(detailPO.getProjectType());
        detailInfo.setFourthLevelBusiness(detailPO.getFourthLevelBusiness());
        // 注意：数据库字段是businessArea，但领域实体是businessSubcategory
        detailInfo.setBusinessSubcategory(detailPO.getBusinessArea());
        detailInfo.setRevenueBudget(detailPO.getRevenueBudget());
        detailInfo.setDirectCostBudget(detailPO.getDirectCostBudget());
        detailInfo.setOtherCostBudget(detailPO.getOtherCostBudget());
        detailInfo.setProfitBudget(detailPO.getProfitBudget());

        return detailInfo;
    }

    /**
     * 数据库对象转换为领域实体直接成本
     */
    private CostAnnualBudgetEntity.DirectCostInfo convertToDirectCostInfo(CostAnnualBudgetDetailDirectCost directCostPO) {
        CostAnnualBudgetEntity.DirectCostInfo directCostInfo = new CostAnnualBudgetEntity.DirectCostInfo();

        directCostInfo.setId(directCostPO.getId());
        directCostInfo.setBudgetDetailId(directCostPO.getBudgetDetailId());
        // 注意：数据库字段映射
        directCostInfo.setSubjectCode(directCostPO.getCostItem()); // 费用科目 -> 预算科目编码
        directCostInfo.setSubjectName(directCostPO.getCostItem()); // 费用科目 -> 预算科目名称
        directCostInfo.setSubjectDescription(directCostPO.getCostDescription()); // 科目释义
        directCostInfo.setCostAmount(directCostPO.getCostAmount());

        return directCostInfo;
    }
}
