package com.cdkit.modules.cm.api.budget.dto;

import com.cdkitframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 年度预算明细导入DTO
 * <AUTHOR>
 * @date 2025-08-04
 */
@Schema(description = "年度预算明细导入DTO")
@Data
public class CostAnnualBudgetDetailImportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**所属单位（全称）*/
    @Excel(name = "所属单位（全称）", width = 20)
    @Schema(description = "所属单位（全称）")
    private String professionalCompany;

    /**下属中心（全称）*/
    @Excel(name = "下属中心（全称）", width = 20)
    @Schema(description = "下属中心（全称）")
    private String center;

    /**年度预算编号（每年首次导入时无需填写）*/
    @Excel(name = "年度预算编号（每年首次导入时无需填写）", width = 25)
    @Schema(description = "年度预算编号（每年首次导入时无需填写）")
    private String budgetCode;

    /**年度预算项目名称*/
    @Excel(name = "年度预算项目名称", width = 25)
    @Schema(description = "年度预算项目名称")
    private String projectName;

    /**预算类型（常规项目、风险项目、待开发项目、待开发转常规、待开发转风险）*/
    @Excel(name = "预算类型（常规项目、风险项目、待开发项目、待开发转常规、待开发转风险）", width = 30)
    @Schema(description = "预算类型")
    private String budgetType;

    /**WBS编号*/
    @Excel(name = "WBS编号", width = 15)
    @Schema(description = "WBS编号")
    private String wbsCode;

    /**项目类型*/
    @Excel(name = "项目类型", width = 15)
    @Schema(description = "项目类型")
    private String projectType;

    /**四级业务*/
    @Excel(name = "四级业务", width = 15)
    @Schema(description = "四级业务")
    private String fourthLevelBusiness;

    /**业务小类*/
    @Excel(name = "业务小类", width = 15)
    @Schema(description = "业务小类")
    private String businessSubcategory;

    /**收入预算总额（元）*/
    @Excel(name = "收入预算总额（元）", width = 15)
    @Schema(description = "收入预算总额（元）")
    private BigDecimal revenueBudget;

    // 直接成本科目字段
    /**原材料及主要材料*/
    @Excel(name = "原材料及主要材料", width = 15)
    @Schema(description = "原材料及主要材料")
    private BigDecimal rawMaterials;

    /**燃料动力*/
    @Excel(name = "燃料动力", width = 15)
    @Schema(description = "燃料动力")
    private BigDecimal fuelPower;

    /**人工成本*/
    @Excel(name = "人工成本", width = 15)
    @Schema(description = "人工成本")
    private BigDecimal laborCost;

    /**税费*/
    @Excel(name = "税费", width = 15)
    @Schema(description = "税费")
    private BigDecimal taxes;

    /**保险费*/
    @Excel(name = "保险费", width = 15)
    @Schema(description = "保险费")
    private BigDecimal insuranceFee;

    /**差旅费*/
    @Excel(name = "差旅费", width = 15)
    @Schema(description = "差旅费")
    private BigDecimal travelExpense;

    /**出国人员费*/
    @Excel(name = "出国人员费", width = 15)
    @Schema(description = "出国人员费")
    private BigDecimal overseasPersonnelFee;

    /**海餐费*/
    @Excel(name = "海餐费", width = 15)
    @Schema(description = "海餐费")
    private BigDecimal seaMealFee;

    /**物业管理费*/
    @Excel(name = "物业管理费", width = 15)
    @Schema(description = "物业管理费")
    private BigDecimal propertyManagementFee;

    /**固定资产折旧*/
    @Excel(name = "固定资产折旧", width = 15)
    @Schema(description = "固定资产折旧")
    private BigDecimal fixedAssetDepreciation;

    /**无形资产摊销*/
    @Excel(name = "无形资产摊销", width = 15)
    @Schema(description = "无形资产摊销")
    private BigDecimal intangibleAssetAmortization;

    /**长期待摊费用摊销*/
    @Excel(name = "长期待摊费用摊销", width = 15)
    @Schema(description = "长期待摊费用摊销")
    private BigDecimal longTermPrepaidExpenseAmortization;

    /**投资性房地产折旧*/
    @Excel(name = "投资性房地产折旧", width = 15)
    @Schema(description = "投资性房地产折旧")
    private BigDecimal investmentPropertyDepreciation;

    /**使用权资产折旧*/
    @Excel(name = "使用权资产折旧", width = 15)
    @Schema(description = "使用权资产折旧")
    private BigDecimal rightOfUseAssetDepreciation;

    /**其他*/
    @Excel(name = "其他", width = 15)
    @Schema(description = "其他")
    private BigDecimal other;

    /**物料消耗-办公耗材*/
    @Excel(name = "物料消耗-办公耗材", width = 15)
    @Schema(description = "物料消耗-办公耗材")
    private BigDecimal officeSupplies;

    /**物料消耗-一般材料*/
    @Excel(name = "物料消耗-一般材料", width = 15)
    @Schema(description = "物料消耗-一般材料")
    private BigDecimal generalMaterials;

    /**低值易耗品*/
    @Excel(name = "低值易耗品", width = 15)
    @Schema(description = "低值易耗品")
    private BigDecimal lowValueConsumables;

    /**运输费*/
    @Excel(name = "运输费", width = 15)
    @Schema(description = "运输费")
    private BigDecimal transportationFee;

    /**装卸费*/
    @Excel(name = "装卸费", width = 15)
    @Schema(description = "装卸费")
    private BigDecimal loadingUnloadingFee;

    /**港杂费*/
    @Excel(name = "港杂费", width = 15)
    @Schema(description = "港杂费")
    private BigDecimal portMiscellaneousFee;

    /**船舶费*/
    @Excel(name = "船舶费", width = 15)
    @Schema(description = "船舶费")
    private BigDecimal shipFee;

    /**船检费*/
    @Excel(name = "船检费", width = 15)
    @Schema(description = "船检费")
    private BigDecimal shipInspectionFee;

    /**飞机费*/
    @Excel(name = "飞机费", width = 15)
    @Schema(description = "飞机费")
    private BigDecimal aircraftFee;

    /**码头费*/
    @Excel(name = "码头费", width = 15)
    @Schema(description = "码头费")
    private BigDecimal wharfFee;

    /**仓储费*/
    @Excel(name = "仓储费", width = 15)
    @Schema(description = "仓储费")
    private BigDecimal storageFee;

    /**租赁费*/
    @Excel(name = "租赁费", width = 15)
    @Schema(description = "租赁费")
    private BigDecimal leaseFee;

    /**加工费*/
    @Excel(name = "加工费", width = 15)
    @Schema(description = "加工费")
    private BigDecimal processingFee;

    /**外包费-工程外包*/
    @Excel(name = "外包费-工程外包", width = 15)
    @Schema(description = "外包费-工程外包")
    private BigDecimal engineeringOutsourcing;

    /**外包费-技术外包*/
    @Excel(name = "外包费-技术外包", width = 15)
    @Schema(description = "外包费-技术外包")
    private BigDecimal technicalOutsourcing;

    /**外包费-劳务外包*/
    @Excel(name = "外包费-劳务外包", width = 15)
    @Schema(description = "外包费-劳务外包")
    private BigDecimal laborOutsourcing;

    /**修理费*/
    @Excel(name = "修理费", width = 15)
    @Schema(description = "修理费")
    private BigDecimal repairFee;

    /**办公费*/
    @Excel(name = "办公费", width = 15)
    @Schema(description = "办公费")
    private BigDecimal officeFee;

    /**办证费*/
    @Excel(name = "办证费", width = 15)
    @Schema(description = "办证费")
    private BigDecimal certificateFee;

    /**翻译费*/
    @Excel(name = "翻译费", width = 15)
    @Schema(description = "翻译费")
    private BigDecimal translationFee;

    /**取暖费*/
    @Excel(name = "取暖费", width = 15)
    @Schema(description = "取暖费")
    private BigDecimal heatingFee;

    /**通讯费*/
    @Excel(name = "通讯费", width = 15)
    @Schema(description = "通讯费")
    private BigDecimal communicationFee;

    /**图书资料费*/
    @Excel(name = "图书资料费", width = 15)
    @Schema(description = "图书资料费")
    private BigDecimal bookMaterialFee;

    /**印刷出版费*/
    @Excel(name = "印刷出版费", width = 15)
    @Schema(description = "印刷出版费")
    private BigDecimal printingPublishingFee;

    /**业主服务费*/
    @Excel(name = "业主服务费", width = 15)
    @Schema(description = "业主服务费")
    private BigDecimal ownerServiceFee;

    /**档案管理费*/
    @Excel(name = "档案管理费", width = 15)
    @Schema(description = "档案管理费")
    private BigDecimal archiveManagementFee;

    /**信息系统*/
    @Excel(name = "信息系统", width = 15)
    @Schema(description = "信息系统")
    private BigDecimal informationSystem;

    /**健康安全环保费*/
    @Excel(name = "健康安全环保费", width = 15)
    @Schema(description = "健康安全环保费")
    private BigDecimal healthSafetyEnvironmentalFee;

    /**体系认证费*/
    @Excel(name = "体系认证费", width = 15)
    @Schema(description = "体系认证费")
    private BigDecimal systemCertificationFee;

    /**检(化)验费*/
    @Excel(name = "检(化)验费", width = 15)
    @Schema(description = "检(化)验费")
    private BigDecimal inspectionTestFee;

    /**代理费*/
    @Excel(name = "代理费", width = 15)
    @Schema(description = "代理费")
    private BigDecimal agencyFee;

    /**经营性培训服务费*/
    @Excel(name = "经营性培训服务费", width = 15)
    @Schema(description = "经营性培训服务费")
    private BigDecimal businessTrainingServiceFee;

    /**经营性会务服务费*/
    @Excel(name = "经营性会务服务费", width = 15)
    @Schema(description = "经营性会务服务费")
    private BigDecimal businessConferenceServiceFee;

    /**咨询费*/
    @Excel(name = "咨询费", width = 15)
    @Schema(description = "咨询费")
    private BigDecimal consultingFee;

    /**审计费*/
    @Excel(name = "审计费", width = 15)
    @Schema(description = "审计费")
    private BigDecimal auditFee;

    /**诉讼费*/
    @Excel(name = "诉讼费", width = 15)
    @Schema(description = "诉讼费")
    private BigDecimal litigationFee;

    /**广告费*/
    @Excel(name = "广告费", width = 15)
    @Schema(description = "广告费")
    private BigDecimal advertisingFee;

    /**宣传费*/
    @Excel(name = "宣传费", width = 15)
    @Schema(description = "宣传费")
    private BigDecimal publicityFee;

    /**设计费*/
    @Excel(name = "设计费", width = 15)
    @Schema(description = "设计费")
    private BigDecimal designFee;

    /**专家费*/
    @Excel(name = "专家费", width = 15)
    @Schema(description = "专家费")
    private BigDecimal expertFee;

    /**共享业务服务费*/
    @Excel(name = "共享业务服务费", width = 15)
    @Schema(description = "共享业务服务费")
    private BigDecimal sharedBusinessServiceFee;

    /**会议费*/
    @Excel(name = "会议费", width = 15)
    @Schema(description = "会议费")
    private BigDecimal conferenceFee;

    /**外宾招待费*/
    @Excel(name = "外宾招待费", width = 15)
    @Schema(description = "外宾招待费")
    private BigDecimal foreignGuestEntertainmentFee;

    /**业务招待费*/
    @Excel(name = "业务招待费", width = 15)
    @Schema(description = "业务招待费")
    private BigDecimal businessEntertainmentFee;

    /**离退休费用*/
    @Excel(name = "离退休费用", width = 15)
    @Schema(description = "离退休费用")
    private BigDecimal retirementExpense;

    /**党团协会费用*/
    @Excel(name = "党团协会费用", width = 15)
    @Schema(description = "党团协会费用")
    private BigDecimal partyAssociationFee;

    /**防疫费*/
    @Excel(name = "防疫费", width = 15)
    @Schema(description = "防疫费")
    private BigDecimal epidemicPreventionFee;

    /**协会(学会)费*/
    @Excel(name = "协会(学会)费", width = 15)
    @Schema(description = "协会(学会)费")
    private BigDecimal associationFee;

    /**内部协同成本*/
    @Excel(name = "内部协同成本", width = 15)
    @Schema(description = "内部协同成本")
    private BigDecimal internalCollaborationCost;
}
